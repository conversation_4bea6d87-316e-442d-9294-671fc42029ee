"""
Tests for the WebSocket functionality.
"""
from django.test import TestCase
from django.contrib.auth.models import User
from channels.testing import WebsocketCommunicator
from channels.routing import URLRouter
from channels.auth import AuthMiddlewareStack
from django.urls import path
from ..consumers import AppBuilderConsumer
import json
import asyncio

class WebSocketTests(TestCase):
    """
    Test the WebSocket functionality.
    """
    async def test_connect(self):
        """
        Test connecting to the WebSocket.
        """
        # Create a test user
        user = await self.create_user()
        
        # Create a WebSocket communicator
        communicator = await self.create_communicator()
        
        # Connect to the WebSocket
        connected, _ = await communicator.connect()
        
        # Check that the connection was successful
        self.assertTrue(connected)
        
        # Close the communicator
        await communicator.disconnect()
    
    async def test_receive_message(self):
        """
        Test receiving a message from the WebSocket.
        """
        # Create a test user
        user = await self.create_user()
        
        # Create a WebSocket communicator
        communicator = await self.create_communicator()
        
        # Connect to the WebSocket
        connected, _ = await communicator.connect()
        
        # Check that the connection was successful
        self.assertTrue(connected)
        
        # Send a message to the WebSocket
        await communicator.send_json_to({
            'type': 'echo',
            'message': 'Hello, WebSocket!'
        })
        
        # Receive the response
        response = await communicator.receive_json_from()
        
        # Check the response
        self.assertEqual(response['type'], 'echo')
        self.assertEqual(response['message'], 'Hello, WebSocket!')
        
        # Close the communicator
        await communicator.disconnect()
    
    async def test_broadcast_message(self):
        """
        Test broadcasting a message to all connected clients.
        """
        # Create a test user
        user = await self.create_user()
        
        # Create two WebSocket communicators
        communicator1 = await self.create_communicator()
        communicator2 = await self.create_communicator()
        
        # Connect to the WebSocket
        connected1, _ = await communicator1.connect()
        connected2, _ = await communicator2.connect()
        
        # Check that the connections were successful
        self.assertTrue(connected1)
        self.assertTrue(connected2)
        
        # Send a broadcast message to the WebSocket
        await communicator1.send_json_to({
            'type': 'broadcast',
            'message': 'Hello, everyone!'
        })
        
        # Receive the response on both communicators
        response1 = await communicator1.receive_json_from()
        response2 = await communicator2.receive_json_from()
        
        # Check the responses
        self.assertEqual(response1['type'], 'broadcast')
        self.assertEqual(response1['message'], 'Hello, everyone!')
        self.assertEqual(response2['type'], 'broadcast')
        self.assertEqual(response2['message'], 'Hello, everyone!')
        
        # Close the communicators
        await communicator1.disconnect()
        await communicator2.disconnect()
    
    async def test_disconnect(self):
        """
        Test disconnecting from the WebSocket.
        """
        # Create a test user
        user = await self.create_user()
        
        # Create a WebSocket communicator
        communicator = await self.create_communicator()
        
        # Connect to the WebSocket
        connected, _ = await communicator.connect()
        
        # Check that the connection was successful
        self.assertTrue(connected)
        
        # Disconnect from the WebSocket
        await communicator.disconnect()
        
        # Try to send a message after disconnecting
        with self.assertRaises(Exception):
            await communicator.send_json_to({
                'type': 'echo',
                'message': 'Hello, WebSocket!'
            })
    
    async def create_user(self):
        """
        Create a test user.
        """
        loop = asyncio.get_event_loop()
        user = await loop.run_in_executor(
            None,
            lambda: User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpassword'
            )
        )
        return user
    
    async def create_communicator(self):
        """
        Create a WebSocket communicator.
        """
        # Create a URL router
        application = URLRouter([
            path('ws/app_builder/', AppBuilderConsumer.as_asgi()),
        ])
        
        # Create a WebSocket communicator
        return WebsocketCommunicator(
            application=application,
            path='/ws/app_builder/'
        )
