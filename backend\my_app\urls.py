# backend/my_app/urls.py
from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from graphene_django.views import GraphQLView
from django.views.decorators.csrf import csrf_exempt
from . import views
from . import api_views
from . import auth_views
from . import api_docs
from . import api_keys
from .schema import schema

# Create a router for our REST API v1
router_v1 = DefaultRouter()
router_v1.register(r'apps', api_views.AppViewSet, basename='app')
router_v1.register(r'component-templates', api_views.ComponentTemplateViewSet, basename='component-template')

# Create a router for our REST API v2 (for future use)
router_v2 = DefaultRouter()
router_v2.register(r'apps', api_views.AppViewSet, basename='app')
router_v2.register(r'component-templates', api_views.ComponentTemplateViewSet, basename='component-template')

urlpatterns = [
    # Legacy endpoints for backward compatibility
    path('get_app_data/', api_views.get_app_data, name='get_app_data'),
    path('save_app_data/', api_views.save_app_data, name='save_app_data'),
    path('export_app_data/', api_views.export_app_data, name='export_app_data'),
    path('import_app_data/', api_views.import_app_data, name='import_app_data'),

    # Original endpoints
    path('get_plugin_props/<str:plugin>/', views.get_plugin_props, name='get_plugin_props'),
    path('generate_ai_suggestions/', views.generate_ai_suggestions, name='generate_ai_suggestions'),
    path('generate_image/', views.generate_image, name='generate_image'),

    # Health check endpoints
    path('api/health/', api_views.health_check, name='health_check'),
    path('api/status/', views.api_status, name='api_status'),
    path('api/health-check', views.health_check, name='health_check'),

    # Error reporting endpoint
    path('api/errors/', api_views.report_errors, name='report_errors'),

    # API Keys endpoints
    path('api/api-keys/', api_keys.get_api_keys, name='get_api_keys'),
    path('api/validate-api-key/', api_keys.validate_api_key, name='validate_api_key'),

    # API documentation
    path('api/docs/', api_docs.api_docs, name='api_docs'),

    # REST API endpoints with versioning
    path('api/v1/', include(router_v1.urls)),
    path('api/v2/', include(router_v2.urls)),

    # REST API endpoints without versioning (for backward compatibility)
    path('api/', include(router_v1.urls)),

    # GraphQL endpoint
    path('graphql/', csrf_exempt(GraphQLView.as_view(graphiql=True, schema=schema)), name='graphql'),
    path('graphql-docs/', views.graphql_docs, name='graphql_docs'),

    # Authentication endpoints for the REST API
    path('api-auth/', include('rest_framework.urls', namespace='rest_framework')),
    path('api-token-auth/', auth_views.CustomAuthToken.as_view(), name='api_token_auth'),
    path('api-register/', auth_views.register_user, name='api_register'),

    # JWT Authentication endpoints
    path('auth/login/', auth_views.jwt_login, name='jwt_login'),
    path('auth/register/', auth_views.register_user, name='jwt_register'),
    path('auth/profile/', auth_views.user_profile, name='user_profile'),
    path('auth/profile/update/', auth_views.update_profile, name='update_profile'),

    # Index page
    path('', views.index, name='index'),

    # Swagger UI
    path('swagger/', views.swagger_ui, name='swagger_ui'),

    # Login test
    path('login-test/', views.login_test, name='login_test'),

    # Custom login
    path('custom-login/', views.custom_login, name='custom_login'),
    path('custom-login-api/', views.custom_login_api, name='custom_login_api'),
]
