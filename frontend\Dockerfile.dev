# Development Dockerfile for frontend
FROM node:20-alpine

# Install build dependencies
RUN apk add --no-cache \
    autoconf \
    automake \
    libtool \
    nasm \
    make \
    g++ \
    python3 \
    git

# Set working directory
WORKDIR /app

# Configure npm with more memory and legacy peer deps
RUN npm config set fund false && \
    npm config set audit false && \
    npm config set loglevel warn && \
    npm config set legacy-peer-deps true

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies with fallback options
RUN npm install --no-fund --no-audit || \
    npm install --no-fund --no-audit --legacy-peer-deps || \
    npm install --no-fund --no-audit --force

# Copy the rest of the application
COPY . .

# Expose port
EXPOSE 3000

# Start webpack-dev-server
CMD ["npm", "start"]




