/**
 * End-to-end tests for the App Builder application.
 */

describe('App Builder', () => {
  beforeEach(() => {
    // Visit the home page before each test
    cy.visit('/');
  });

  it('should load the home page', () => {
    // Check that the page has loaded
    cy.get('.App').should('exist');
    cy.get('.App-nav').should('exist');
    cy.get('.App-main').should('exist');
    
    // Check that the title is correct
    cy.title().should('include', 'App Builder');
  });

  it('should navigate to the App Builder page', () => {
    // Click on the App Builder link
    cy.contains('App Builder').click();
    
    // Check that the URL has changed
    cy.url().should('include', '/app-builder');
    
    // Check that the App Builder page has loaded
    cy.get('.app-builder-container').should('exist');
  });

  it('should navigate to the WebSocket Manager page', () => {
    // Click on the WebSocket Manager link
    cy.contains('WebSocket Manager').click();
    
    // Check that the URL has changed
    cy.url().should('include', '/websocket');
    
    // Check that the WebSocket Manager page has loaded
    cy.get('.websocket-manager-container').should('exist');
  });

  it('should navigate to the WebSocket Test page', () => {
    // Click on the WebSocket Test link
    cy.contains('WebSocket Test').click();
    
    // Check that the URL has changed
    cy.url().should('include', '/websocket-test');
    
    // Check that the WebSocket Test page has loaded
    cy.get('.websocket-test-container').should('exist');
  });

  it('should navigate to the Collaboration page', () => {
    // Click on the Collaboration link
    cy.contains('Collaboration').click();
    
    // Check that the URL has changed
    cy.url().should('include', '/collaboration');
    
    // Check that the Collaboration page has loaded
    cy.get('.collaboration-container').should('exist');
  });
});

describe('Authentication', () => {
  beforeEach(() => {
    // Visit the home page before each test
    cy.visit('/');
  });

  it('should show the login form', () => {
    // Click on the login button
    cy.contains('Login').click();
    
    // Check that the login form is displayed
    cy.get('.login-form').should('exist');
    cy.get('input[name="username"]').should('exist');
    cy.get('input[name="password"]').should('exist');
    cy.get('button[type="submit"]').should('exist');
  });

  it('should show an error message for invalid credentials', () => {
    // Click on the login button
    cy.contains('Login').click();
    
    // Fill in the login form with invalid credentials
    cy.get('input[name="username"]').type('invaliduser');
    cy.get('input[name="password"]').type('invalidpassword');
    
    // Submit the form
    cy.get('button[type="submit"]').click();
    
    // Check that an error message is displayed
    cy.get('.error-message').should('exist');
    cy.get('.error-message').should('contain', 'Invalid username or password');
  });

  it('should login successfully with valid credentials', () => {
    // Create a test user
    cy.request('POST', '/api/auth/register/', {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'testpassword'
    });
    
    // Click on the login button
    cy.contains('Login').click();
    
    // Fill in the login form with valid credentials
    cy.get('input[name="username"]').type('testuser');
    cy.get('input[name="password"]').type('testpassword');
    
    // Submit the form
    cy.get('button[type="submit"]').click();
    
    // Check that the user is logged in
    cy.get('.user-profile').should('exist');
    cy.get('.user-profile').should('contain', 'testuser');
  });

  it('should logout successfully', () => {
    // Create a test user
    cy.request('POST', '/api/auth/register/', {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'testpassword'
    });
    
    // Login
    cy.request('POST', '/api/auth/login/', {
      username: 'testuser',
      password: 'testpassword'
    }).then((response) => {
      // Set the token in localStorage
      window.localStorage.setItem('token', response.body.jwt_token);
      
      // Reload the page
      cy.visit('/');
      
      // Check that the user is logged in
      cy.get('.user-profile').should('exist');
      cy.get('.user-profile').should('contain', 'testuser');
      
      // Click on the logout button
      cy.contains('Logout').click();
      
      // Check that the user is logged out
      cy.get('.user-profile').should('not.exist');
      cy.contains('Login').should('exist');
    });
  });
});

describe('WebSocket Functionality', () => {
  beforeEach(() => {
    // Visit the WebSocket Test page before each test
    cy.visit('/websocket-test');
  });

  it('should connect to the WebSocket server', () => {
    // Check that the WebSocket status is connected
    cy.get('.websocket-status').should('contain', 'Connected');
  });

  it('should send and receive messages', () => {
    // Type a message
    cy.get('input[name="message"]').type('Hello, WebSocket!');
    
    // Send the message
    cy.get('button').contains('Send').click();
    
    // Check that the message was sent
    cy.get('.message-list').should('contain', 'Hello, WebSocket!');
    
    // Check that a response was received
    cy.get('.message-list').should('contain', 'Echo: Hello, WebSocket!');
  });

  it('should handle WebSocket disconnection', () => {
    // Click the disconnect button
    cy.get('button').contains('Disconnect').click();
    
    // Check that the WebSocket status is disconnected
    cy.get('.websocket-status').should('contain', 'Disconnected');
    
    // Try to send a message
    cy.get('input[name="message"]').type('This should not be sent');
    cy.get('button').contains('Send').click();
    
    // Check that an error message is displayed
    cy.get('.error-message').should('exist');
    cy.get('.error-message').should('contain', 'WebSocket is not connected');
  });

  it('should reconnect to the WebSocket server', () => {
    // Click the disconnect button
    cy.get('button').contains('Disconnect').click();
    
    // Check that the WebSocket status is disconnected
    cy.get('.websocket-status').should('contain', 'Disconnected');
    
    // Click the connect button
    cy.get('button').contains('Connect').click();
    
    // Check that the WebSocket status is connected
    cy.get('.websocket-status').should('contain', 'Connected');
  });
});
