const express = require('express');
const path = require('path');
const http = require('http');
const app = express();
const PORT = process.env.PORT || 3000;
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';

// Parse the backend URL to get hostname and port
const backendUrl = new URL(BACKEND_URL);

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Proxy API requests to the backend
app.use('/api', (req, res) => {
  const options = {
    hostname: backendUrl.hostname,
    port: backendUrl.port || 8000,
    path: req.url,
    method: req.method,
    headers: req.headers
  };

  console.log(`Proxying request to ${backendUrl.hostname}:${backendUrl.port || 8000}${req.url}`);

  const proxyReq = http.request(options, (proxyRes) => {
    res.writeHead(proxyRes.statusCode, proxyRes.headers);
    proxyRes.pipe(res);
  });

  proxyReq.on('error', (e) => {
    console.error(`Proxy request error: ${e.message}`);
    res.status(503).json({
      error: 'Backend service unavailable',
      message: e.message
    });
  });

  // Add timeout handling
  proxyReq.setTimeout(5000, () => {
    proxyReq.abort();
    res.status(504).json({
      error: 'Gateway timeout',
      message: 'Backend server did not respond in time'
    });
  });

  if (req.body) {
    proxyReq.write(JSON.stringify(req.body));
  }

  proxyReq.end();
});

// Serve index.html for all other routes (SPA support)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'fallback.html'));
});

// Start the server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running at http://0.0.0.0:${PORT}/`);
});

