import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import ThemeSwitcher from './common/ThemeSwitcher';

const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
`;

const Header = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 64px;
  background-color: var(--background-secondary, #f5f5f5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Logo = styled.div`
  font-size: 1.5rem;
  font-weight: bold;

  a {
    color: var(--primary-color);
    text-decoration: none;

    &:hover {
      text-decoration: none;
      opacity: 0.9;
    }
  }
`;

const Nav = styled.nav`
  ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      margin-left: 20px;

      a {
        color: var(--text-color);
        text-decoration: none;
        padding: 8px 12px;
        border-radius: 4px;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        &.active {
          color: var(--primary-color);
          font-weight: 500;
        }
      }
    }
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 20px;
`;

const Footer = styled.footer`
  padding: 20px;
  text-align: center;
  background-color: var(--background-secondary, #f5f5f5);
  border-top: 1px solid var(--border-color, #e8e8e8);
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
`;

/**
 * Main layout component
 * Enhanced with styled components and theme switcher
 */
const Layout = ({ children }) => {
  return (
    <LayoutContainer className="app-layout">
      <Header className="app-header">
        <Logo className="logo">
          <Link to="/">App Builder</Link>
        </Logo>

        <Nav className="main-nav">
          <ul>
            <li><Link to="/">Home</Link></li>
            <li><Link to="/app-builder">App Builder</Link></li>
            <li><Link to="/websocket">WebSocket</Link></li>
          </ul>
        </Nav>

        <HeaderRight>
          <ThemeSwitcher position="dropdown" />
        </HeaderRight>
      </Header>

      <Main className="app-main">
        {children}
      </Main>

      <Footer className="app-footer">
        <p>&copy; {new Date().getFullYear()} App Builder</p>
      </Footer>
    </LayoutContainer>
  );
};

export default Layout;
