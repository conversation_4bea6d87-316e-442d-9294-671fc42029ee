import React, { useEffect, useState } from 'react';

const ServerStatusChecker = ({ onStatusChange }) => {
  const [status, setStatus] = useState('checking');

  useEffect(() => {
    const checkBackendStatus = async () => {
      try {
        const response = await fetch('/api/health-check', { 
          method: 'GET',
          headers: { 'Cache-Control': 'no-cache' },
          timeout: 5000
        });
        
        if (response.ok) {
          setStatus('online');
          if (onStatusChange) onStatusChange('online');
        } else {
          setStatus('error');
          if (onStatusChange) onStatusChange('error');
        }
      } catch (error) {
        console.error('Backend status check failed:', error);
        setStatus('offline');
        if (onStatusChange) onStatusChange('offline');
      }
    };

    // Check immediately
    checkBackendStatus();
    
    // Then check every 30 seconds
    const intervalId = setInterval(checkBackendStatus, 30000);
    
    return () => clearInterval(intervalId);
  }, [onStatusChange]);

  // This component doesn't render anything visible
  return null;
};

export default ServerStatusChecker;