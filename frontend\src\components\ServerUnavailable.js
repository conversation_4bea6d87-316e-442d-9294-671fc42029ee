import React from 'react';

const ServerUnavailable = ({ onRetry }) => {
  return (
    <div className="server-unavailable">
      <h2>Server Unavailable</h2>
      <p>We're having trouble connecting to the server. This could be due to:</p>
      <ul>
        <li>The server is currently down or restarting</li>
        <li>Your internet connection is unstable</li>
        <li>A firewall is blocking the connection</li>
      </ul>
      
      <div className="actions">
        <button onClick={onRetry} className="retry-button">
          Retry Connection
        </button>
        
        <div className="troubleshooting">
          <h3>Troubleshooting Steps:</h3>
          <ol>
            <li>Check your internet connection</li>
            <li>Make sure the backend server is running (localhost:8000)</li>
            <li>Check browser console for specific error messages</li>
            <li>Clear your browser cache and cookies</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default ServerUnavailable;