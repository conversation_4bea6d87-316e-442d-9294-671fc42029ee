/**
 * API caching utilities for the App Builder application.
 */

// Cache storage
const cache = new Map();

// Cache configuration
const defaultConfig = {
  maxAge: 5 * 60 * 1000, // 5 minutes
  staleWhileRevalidate: true,
  maxSize: 100, // Maximum number of cached items
};

/**
 * Set cache configuration.
 * 
 * @param {Object} config - Cache configuration
 * @param {number} config.maxAge - Maximum age of cached items in milliseconds
 * @param {boolean} config.staleWhileRevalidate - Whether to return stale data while revalidating
 * @param {number} config.maxSize - Maximum number of cached items
 */
export const setCacheConfig = (config) => {
  Object.assign(defaultConfig, config);
};

/**
 * Generate a cache key from a URL and parameters.
 * 
 * @param {string} url - The URL
 * @param {Object} params - The parameters
 * @returns {string} - The cache key
 */
export const generateCacheKey = (url, params = {}) => {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((acc, key) => {
      acc[key] = params[key];
      return acc;
    }, {});
  
  return `${url}:${JSON.stringify(sortedParams)}`;
};

/**
 * Get an item from the cache.
 * 
 * @param {string} key - The cache key
 * @returns {Object|null} - The cached item or null if not found
 */
export const getCacheItem = (key) => {
  if (!cache.has(key)) {
    return null;
  }
  
  const item = cache.get(key);
  const now = Date.now();
  
  // Check if the item is expired
  if (now - item.timestamp > defaultConfig.maxAge) {
    // If staleWhileRevalidate is enabled, return the stale data
    if (defaultConfig.staleWhileRevalidate) {
      item.isStale = true;
      return item;
    }
    
    // Otherwise, remove the item from the cache
    cache.delete(key);
    return null;
  }
  
  return item;
};

/**
 * Set an item in the cache.
 * 
 * @param {string} key - The cache key
 * @param {*} data - The data to cache
 */
export const setCacheItem = (key, data) => {
  // Check if the cache is full
  if (cache.size >= defaultConfig.maxSize) {
    // Remove the oldest item
    const oldestKey = Array.from(cache.keys())[0];
    cache.delete(oldestKey);
  }
  
  cache.set(key, {
    data,
    timestamp: Date.now(),
    isStale: false,
  });
};

/**
 * Clear the cache.
 * 
 * @param {string} keyPrefix - Optional key prefix to clear only matching items
 */
export const clearCache = (keyPrefix) => {
  if (keyPrefix) {
    // Clear only items with matching key prefix
    for (const key of cache.keys()) {
      if (key.startsWith(keyPrefix)) {
        cache.delete(key);
      }
    }
  } else {
    // Clear the entire cache
    cache.clear();
  }
};

/**
 * Fetch data with caching.
 * 
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @param {Object} cacheOptions - Cache options
 * @param {boolean} cacheOptions.bypassCache - Whether to bypass the cache
 * @param {number} cacheOptions.maxAge - Maximum age of cached items in milliseconds
 * @returns {Promise<*>} - The fetched data
 */
export const fetchWithCache = async (url, options = {}, cacheOptions = {}) => {
  const { bypassCache = false, maxAge = defaultConfig.maxAge } = cacheOptions;
  
  // Generate a cache key
  const cacheKey = generateCacheKey(url, options.body);
  
  // Check if the data is in the cache
  if (!bypassCache) {
    const cachedItem = getCacheItem(cacheKey);
    
    if (cachedItem && !cachedItem.isStale) {
      return cachedItem.data;
    }
    
    // If the data is stale, revalidate it in the background
    if (cachedItem && cachedItem.isStale) {
      // Return the stale data immediately
      const staleData = cachedItem.data;
      
      // Revalidate the data in the background
      fetch(url, options)
        .then((response) => response.json())
        .then((data) => {
          setCacheItem(cacheKey, data);
        })
        .catch((error) => {
          console.error('Error revalidating cached data:', error);
        });
      
      return staleData;
    }
  }
  
  // Fetch the data
  const response = await fetch(url, options);
  const data = await response.json();
  
  // Cache the data
  setCacheItem(cacheKey, data);
  
  return data;
};

/**
 * Create a cached API client.
 * 
 * @param {Object} baseOptions - Base fetch options
 * @returns {Object} - The cached API client
 */
export const createCachedApiClient = (baseOptions = {}) => {
  return {
    /**
     * Fetch data with caching.
     * 
     * @param {string} url - The URL to fetch
     * @param {Object} options - Fetch options
     * @param {Object} cacheOptions - Cache options
     * @returns {Promise<*>} - The fetched data
     */
    fetch: (url, options = {}, cacheOptions = {}) => {
      const mergedOptions = { ...baseOptions, ...options };
      return fetchWithCache(url, mergedOptions, cacheOptions);
    },
    
    /**
     * Clear the cache.
     * 
     * @param {string} keyPrefix - Optional key prefix to clear only matching items
     */
    clearCache: (keyPrefix) => {
      clearCache(keyPrefix);
    },
    
    /**
     * Set cache configuration.
     * 
     * @param {Object} config - Cache configuration
     */
    setCacheConfig: (config) => {
      setCacheConfig(config);
    },
  };
};

export default {
  fetchWithCache,
  createCachedApiClient,
  clearCache,
  setCacheConfig,
};
