/**
 * Error Tracking System
 * 
 * This utility provides comprehensive error tracking and reporting functionality.
 * It captures unhandled errors, console errors, network errors, and more.
 */

// Configuration
const config = {
  // Whether error tracking is enabled
  enabled: true,
  
  // Sampling rate (0.0 to 1.0) - what percentage of errors to track
  samplingRate: 1.0,
  
  // Maximum number of errors to store
  errorLimit: 100,
  
  // Maximum number of breadcrumbs to store
  breadcrumbLimit: 50,
  
  // Errors to ignore (regexes)
  ignoredErrors: [
    /ResizeObserver loop limit exceeded/,
    /Loading chunk \d+ failed/,
    /Network request failed/,
    /Script error/,
    /Extension context invalidated/
  ],
  
  // Endpoint to report errors to
  reportingEndpoint: '/api/errors',
  
  // Whether to log errors to console
  logToConsole: true,
  
  // Whether to capture console errors
  captureConsoleErrors: true,
  
  // Whether to capture network errors
  captureNetworkErrors: true,
  
  // Whether to capture unhandled rejections
  captureUnhandledRejections: true,
  
  // Whether to capture breadcrumbs
  captureBreadcrumbs: true
};

// Error storage
const errorStore = {
  errors: [],
  breadcrumbs: [],
  sessionId: generateSessionId(),
  startTime: new Date().toISOString()
};

/**
 * Generate a unique session ID
 * 
 * @returns {string} - A unique session ID
 */
function generateSessionId() {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Initialize the error tracking system
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} - The error tracking API
 */
export function initErrorTracking(options = {}) {
  // Merge options with default config
  Object.assign(config, options);
  
  if (!config.enabled) {
    console.log('Error tracking is disabled');
    return createPublicApi();
  }
  
  // Set up global error handlers
  setupErrorHandlers();
  
  // Log initialization
  console.log('Error tracking initialized');
  
  // Return the public API
  return createPublicApi();
}

/**
 * Set up global error handlers
 */
function setupErrorHandlers() {
  // Handle unhandled errors
  window.addEventListener('error', handleGlobalError);
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', handleUnhandledRejection);
  
  // Capture console errors
  if (config.captureConsoleErrors) {
    setupConsoleCapture();
  }
  
  // Capture network errors
  if (config.captureNetworkErrors) {
    setupNetworkCapture();
  }
  
  // Capture breadcrumbs
  if (config.captureBreadcrumbs) {
    setupBreadcrumbCapture();
  }
}

/**
 * Handle global errors
 * 
 * @param {ErrorEvent} event - The error event
 */
function handleGlobalError(event) {
  // Prevent default browser error handling
  event.preventDefault();
  
  // Track the error
  trackError({
    type: 'uncaught_error',
    message: event.message || 'Unknown error',
    stack: event.error ? event.error.stack : null,
    source: event.filename,
    line: event.lineno,
    column: event.colno,
    timestamp: new Date().toISOString()
  });
  
  // Log to console if enabled
  if (config.logToConsole) {
    console.error('Uncaught error:', event.message);
  }
}

/**
 * Handle unhandled promise rejections
 * 
 * @param {PromiseRejectionEvent} event - The rejection event
 */
function handleUnhandledRejection(event) {
  // Prevent default browser error handling
  event.preventDefault();
  
  // Get error details
  const error = event.reason;
  const message = error instanceof Error ? error.message : String(error);
  const stack = error instanceof Error ? error.stack : null;
  
  // Track the error
  trackError({
    type: 'unhandled_rejection',
    message: message || 'Unhandled promise rejection',
    stack: stack,
    timestamp: new Date().toISOString()
  });
  
  // Log to console if enabled
  if (config.logToConsole) {
    console.error('Unhandled rejection:', message);
  }
}

/**
 * Set up console error capture
 */
function setupConsoleCapture() {
  // Save original console methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  
  // Override console.error
  console.error = function(...args) {
    // Call original method
    originalConsoleError.apply(console, args);
    
    // Track the error
    const message = args.map(arg => 
      typeof arg === 'string' ? arg : JSON.stringify(arg)
    ).join(' ');
    
    trackError({
      type: 'console_error',
      message: message,
      timestamp: new Date().toISOString()
    });
  };
  
  // Override console.warn
  console.warn = function(...args) {
    // Call original method
    originalConsoleWarn.apply(console, args);
    
    // Add breadcrumb
    if (config.captureBreadcrumbs) {
      addBreadcrumb({
        type: 'console_warn',
        message: args.map(arg => 
          typeof arg === 'string' ? arg : JSON.stringify(arg)
        ).join(' '),
        timestamp: new Date().toISOString()
      });
    }
  };
}

/**
 * Set up network error capture
 */
function setupNetworkCapture() {
  // Save original fetch
  const originalFetch = window.fetch;
  
  // Override fetch
  window.fetch = async function(...args) {
    try {
      const response = await originalFetch.apply(window, args);
      
      // Add breadcrumb for successful requests
      if (config.captureBreadcrumbs) {
        addBreadcrumb({
          type: 'network',
          category: 'fetch',
          data: {
            url: typeof args[0] === 'string' ? args[0] : args[0].url,
            method: args[1]?.method || 'GET',
            status: response.status
          },
          timestamp: new Date().toISOString()
        });
      }
      
      // Track error responses
      if (!response.ok) {
        trackError({
          type: 'network_error',
          message: `Fetch error: ${response.status} ${response.statusText}`,
          data: {
            url: typeof args[0] === 'string' ? args[0] : args[0].url,
            method: args[1]?.method || 'GET',
            status: response.status,
            statusText: response.statusText
          },
          timestamp: new Date().toISOString()
        });
      }
      
      return response;
    } catch (error) {
      // Track network errors
      trackError({
        type: 'network_error',
        message: `Fetch failed: ${error.message}`,
        stack: error.stack,
        data: {
          url: typeof args[0] === 'string' ? args[0] : args[0]?.url
        },
        timestamp: new Date().toISOString()
      });
      
      throw error;
    }
  };
  
  // Override XMLHttpRequest
  const originalXhrOpen = XMLHttpRequest.prototype.open;
  const originalXhrSend = XMLHttpRequest.prototype.send;
  
  XMLHttpRequest.prototype.open = function(method, url) {
    this._errorTracking = {
      method,
      url
    };
    return originalXhrOpen.apply(this, arguments);
  };
  
  XMLHttpRequest.prototype.send = function() {
    // Add event listeners
    this.addEventListener('load', function() {
      // Add breadcrumb for successful requests
      if (config.captureBreadcrumbs) {
        addBreadcrumb({
          type: 'network',
          category: 'xhr',
          data: {
            url: this._errorTracking?.url,
            method: this._errorTracking?.method,
            status: this.status
          },
          timestamp: new Date().toISOString()
        });
      }
      
      // Track error responses
      if (this.status >= 400) {
        trackError({
          type: 'network_error',
          message: `XHR error: ${this.status} ${this.statusText}`,
          data: {
            url: this._errorTracking?.url,
            method: this._errorTracking?.method,
            status: this.status,
            statusText: this.statusText
          },
          timestamp: new Date().toISOString()
        });
      }
    });
    
    this.addEventListener('error', function() {
      // Track network errors
      trackError({
        type: 'network_error',
        message: 'XHR failed',
        data: {
          url: this._errorTracking?.url,
          method: this._errorTracking?.method
        },
        timestamp: new Date().toISOString()
      });
    });
    
    return originalXhrSend.apply(this, arguments);
  };
}

/**
 * Set up breadcrumb capture
 */
function setupBreadcrumbCapture() {
  // Capture clicks
  document.addEventListener('click', function(event) {
    // Get the clicked element
    const element = event.target;
    
    // Get element details
    const tagName = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const classes = Array.from(element.classList).map(c => `.${c}`).join('');
    const text = element.innerText?.substring(0, 50);
    
    // Add breadcrumb
    addBreadcrumb({
      type: 'user',
      category: 'click',
      data: {
        element: `${tagName}${id}${classes}`,
        text: text
      },
      timestamp: new Date().toISOString()
    });
  });
  
  // Capture navigation
  window.addEventListener('popstate', function() {
    addBreadcrumb({
      type: 'navigation',
      data: {
        from: document.referrer,
        to: window.location.href
      },
      timestamp: new Date().toISOString()
    });
  });
}

/**
 * Track an error
 * 
 * @param {Object} error - The error to track
 */
function trackError(error) {
  // Check if error tracking is enabled
  if (!config.enabled) {
    return;
  }
  
  // Apply sampling rate
  if (Math.random() > config.samplingRate) {
    return;
  }
  
  // Check if error should be ignored
  if (shouldIgnoreError(error)) {
    return;
  }
  
  // Add session information
  error.sessionId = errorStore.sessionId;
  
  // Add user agent
  error.userAgent = navigator.userAgent;
  
  // Add URL
  error.url = window.location.href;
  
  // Add breadcrumbs
  error.breadcrumbs = [...errorStore.breadcrumbs];
  
  // Add to error store
  errorStore.errors.push(error);
  
  // Limit the number of stored errors
  if (errorStore.errors.length > config.errorLimit) {
    errorStore.errors.shift();
  }
  
  // Report the error
  reportError(error);
}

/**
 * Check if an error should be ignored
 * 
 * @param {Object} error - The error to check
 * @returns {boolean} - Whether the error should be ignored
 */
function shouldIgnoreError(error) {
  // Check against ignored errors
  return config.ignoredErrors.some(pattern => {
    if (pattern instanceof RegExp) {
      return pattern.test(error.message);
    }
    return error.message.includes(pattern);
  });
}

/**
 * Add a breadcrumb
 * 
 * @param {Object} breadcrumb - The breadcrumb to add
 */
function addBreadcrumb(breadcrumb) {
  // Check if breadcrumb tracking is enabled
  if (!config.captureBreadcrumbs) {
    return;
  }
  
  // Add to breadcrumb store
  errorStore.breadcrumbs.push(breadcrumb);
  
  // Limit the number of stored breadcrumbs
  if (errorStore.breadcrumbs.length > config.breadcrumbLimit) {
    errorStore.breadcrumbs.shift();
  }
}

/**
 * Report an error to the server
 * 
 * @param {Object} error - The error to report
 */
function reportError(error) {
  // Check if reporting is enabled
  if (!config.reportingEndpoint) {
    return;
  }
  
  // Send the error to the server
  fetch(config.reportingEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(error),
    // Use keepalive to ensure the request is sent even if the page is unloading
    keepalive: true
  }).catch(err => {
    // Log reporting error
    if (config.logToConsole) {
      console.error('Failed to report error:', err);
    }
  });
}

/**
 * Create the public API
 * 
 * @returns {Object} - The public API
 */
function createPublicApi() {
  return {
    /**
     * Track an error manually
     * 
     * @param {Error|string} error - The error to track
     * @param {Object} additionalData - Additional data to include
     */
    trackError: function(error, additionalData = {}) {
      // Convert error to the right format
      const errorObj = error instanceof Error
        ? {
            type: 'manual',
            message: error.message,
            stack: error.stack,
            ...additionalData,
            timestamp: new Date().toISOString()
          }
        : {
            type: 'manual',
            message: String(error),
            ...additionalData,
            timestamp: new Date().toISOString()
          };
      
      // Track the error
      trackError(errorObj);
    },
    
    /**
     * Add a breadcrumb manually
     * 
     * @param {string} message - The breadcrumb message
     * @param {string} category - The breadcrumb category
     * @param {Object} data - Additional data to include
     */
    addBreadcrumb: function(message, category = 'manual', data = {}) {
      addBreadcrumb({
        type: 'manual',
        category,
        message,
        data,
        timestamp: new Date().toISOString()
      });
    },
    
    /**
     * Get all tracked errors
     * 
     * @returns {Array} - The tracked errors
     */
    getErrors: function() {
      return [...errorStore.errors];
    },
    
    /**
     * Get all breadcrumbs
     * 
     * @returns {Array} - The breadcrumbs
     */
    getBreadcrumbs: function() {
      return [...errorStore.breadcrumbs];
    },
    
    /**
     * Clear all tracked errors
     */
    clearErrors: function() {
      errorStore.errors = [];
    },
    
    /**
     * Clear all breadcrumbs
     */
    clearBreadcrumbs: function() {
      errorStore.breadcrumbs = [];
    },
    
    /**
     * Get the current configuration
     * 
     * @returns {Object} - The current configuration
     */
    getConfig: function() {
      return { ...config };
    },
    
    /**
     * Update the configuration
     * 
     * @param {Object} options - The new configuration options
     */
    updateConfig: function(options) {
      Object.assign(config, options);
    }
  };
}

// Create and export the default instance
export default initErrorTracking();
