#!/usr/bin/env node
const http = require('http');
const { exec } = require('child_process');

console.log('Checking server status...');

// Check frontend server
checkServer('http://localhost:3000', 'Frontend', () => {
  // Check backend server
  checkServer('http://localhost:8000/api/health-check', 'Backend', () => {
    console.log('\nAll checks completed.');
  });
});

function checkServer(url, serverName, callback) {
  console.log(`\nChecking ${serverName} server at ${url}...`);
  
  const req = http.get(url, (res) => {
    console.log(`✅ ${serverName} server is running (Status: ${res.statusCode})`);
    callback();
  });
  
  req.on('error', (error) => {
    console.error(`❌ ${serverName} server is not running: ${error.message}`);
    
    if (serverName === 'Frontend') {
      console.log('\nTrying to start frontend server...');
      startFrontendServer(callback);
    } else if (serverName === 'Backend') {
      console.log('\nTrying to start backend server...');
      startBackendServer(callback);
    } else {
      callback();
    }
  });
  
  req.setTimeout(3000, () => {
    req.abort();
    console.error(`❌ ${serverName} server connection timed out`);
    callback();
  });
}

function startFrontendServer(callback) {
  const command = process.platform === 'win32' 
    ? 'cd frontend && npm start' 
    : 'cd frontend && npm start';
  
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error starting frontend server: ${error.message}`);
      console.log('Please start the frontend server manually with: cd frontend && npm start');
    } else {
      console.log('Frontend server starting...');
    }
    callback();
  });
}

function startBackendServer(callback) {
  const command = process.platform === 'win32'
    ? 'cd backend && python manage.py runserver'
    : 'cd backend && python manage.py runserver';
  
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error starting backend server: ${error.message}`);
      console.log('Please start the backend server manually with: cd backend && python manage.py runserver');
    } else {
      console.log('Backend server starting...');
    }
    callback();
  });
}