#!/usr/bin/env node
const http = require('http');
const net = require('net');
const dns = require('dns');
const { execSync } = require('child_process');

console.log('Running network diagnostics...\n');

// Check if localhost resolves
console.log('Checking localhost DNS resolution:');
dns.lookup('localhost', (err, address, family) => {
  if (err) {
    console.error(`❌ Error resolving localhost: ${err.message}`);
  } else {
    console.log(`✅ localhost resolves to ${address} (IPv${family})`);
  }
  
  // Check if port 3000 is in use
  checkPort(3000);
});

function checkPort(port) {
  console.log(`\nChecking if port ${port} is in use:`);
  
  const server = net.createServer();
  
  server.once('error', (err) => {
    if (err.code === 'EADDRINUSE') {
      console.log(`❌ Port ${port} is already in use`);
      
      // Try to identify what's using the port
      try {
        let command = '';
        if (process.platform === 'win32') {
          command = `netstat -ano | findstr :${port}`;
        } else {
          command = `lsof -i :${port}`;
        }
        
        const result = execSync(command).toString();
        console.log('Process using the port:');
        console.log(result);
      } catch (e) {
        console.error(`Could not identify process: ${e.message}`);
      }
    } else {
      console.error(`❌ Error checking port: ${err.message}`);
    }
    
    // Continue with HTTP check
    checkHttp();
  });
  
  server.once('listening', () => {
    console.log(`✅ Port ${port} is available`);
    server.close(() => {
      // Continue with HTTP check
      checkHttp();
    });
  });
  
  server.listen(port);
}

function checkHttp() {
  console.log('\nTesting HTTP connections:');
  
  // Test connections to various endpoints
  const endpoints = [
    { url: 'http://localhost:3000', name: 'Frontend' },
    { url: 'http://localhost:8000', name: 'Backend' },
    { url: 'http://localhost:3000/fallback.html', name: 'Fallback page' },
    { url: 'http://localhost:8000/api/health-check', name: 'Backend health check' }
  ];
  
  let completed = 0;
  
  endpoints.forEach(endpoint => {
    console.log(`Testing connection to ${endpoint.name} (${endpoint.url})...`);
    
    const req = http.get(endpoint.url, (res) => {
      console.log(`✅ Connected to ${endpoint.name} - Status: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (data.length > 0) {
          console.log(`Response data (first 100 chars): ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
        } else {
          console.log('No data received in response');
        }
        
        completed++;
        if (completed === endpoints.length) {
          console.log('\nDiagnostics complete!');
        }
      });
    });
    
    req.on('error', (error) => {
      console.error(`❌ Failed to connect to ${endpoint.name}: ${error.message}`);
      
      completed++;
      if (completed === endpoints.length) {
        console.log('\nDiagnostics complete!');
      }
    });
    
    req.setTimeout(5000, () => {
      req.abort();
      console.error(`❌ Connection to ${endpoint.name} timed out`);
      
      completed++;
      if (completed === endpoints.length) {
        console.log('\nDiagnostics complete!');
      }
    });
  });
}
