# PowerShell script to diagnose network and Docker volume issues

Write-Host "Running network and Docker volume diagnostics..." -ForegroundColor Cyan

# Check if localhost resolves
Write-Host "`nChecking localhost DNS resolution:" -ForegroundColor Yellow
try {
    $ipAddress = [System.Net.Dns]::GetHostAddresses("localhost")
    Write-Host "✅ localhost resolves to $($ipAddress -join ', ')" -ForegroundColor Green
}
catch {
    Write-Host "❌ Error resolving localhost: $_" -ForegroundColor Red
}

# Check if ports are in use
function Test-Port {
    param(
        [int]$Port
    )

    Write-Host "`nChecking if port $Port is in use:" -ForegroundColor Yellow

    try {
        $listener = New-Object System.Net.Sockets.TcpListener([System.Net.IPAddress]::Loopback, $Port)
        $listener.Start()
        Write-Host "✅ Port $Port is available" -ForegroundColor Green
        $listener.Stop()
    }
    catch {
        Write-Host "❌ Port $Port is already in use" -ForegroundColor Red

        # Try to identify what's using the port
        try {
            $process = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue |
            Select-Object -First 1 OwningProcess

            if ($process) {
                $processInfo = Get-Process -Id $process.OwningProcess -ErrorAction SilentlyContinue
                if ($processInfo) {
                    Write-Host "Process using the port: $($processInfo.Name) (PID: $($process.OwningProcess))" -ForegroundColor Yellow
                }
            }
        }
        catch {
            Write-Host "Could not identify process: $_" -ForegroundColor Red
        }
    }
}

Test-Port -Port 3000
Test-Port -Port 8000

# Test HTTP connections
Write-Host "`nTesting HTTP connections:" -ForegroundColor Yellow

function Test-HttpConnection {
    param(
        [string]$Url,
        [string]$Name
    )

    Write-Host "Testing connection to $Name ($Url)..." -ForegroundColor Yellow

    try {
        $request = [System.Net.WebRequest]::Create($Url)
        $request.Timeout = 5000
        $response = $request.GetResponse()

        Write-Host "✅ Connected to $Name - Status: $([int]$response.StatusCode)" -ForegroundColor Green

        $stream = $response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $data = $reader.ReadToEnd()
        $reader.Close()
        $response.Close()

        if ($data.Length -gt 0) {
            $preview = if ($data.Length -gt 100) { $data.Substring(0, 100) + "..." } else { $data }
            Write-Host "Response data (first 100 chars): $preview" -ForegroundColor Gray
        }
        else {
            Write-Host "No data received in response" -ForegroundColor Yellow
        }
    }
    catch [System.Net.WebException] {
        if ($_.Exception.Status -eq [System.Net.WebExceptionStatus]::Timeout) {
            Write-Host "❌ Connection to ${Name} timed out" -ForegroundColor Red
        }
        else {
            Write-Host "❌ Failed to connect to ${Name}: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Error testing connection to ${Name}: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Test-HttpConnection -Url "http://localhost:3000" -Name "Frontend"
Test-HttpConnection -Url "http://localhost:8000" -Name "Backend"
Test-HttpConnection -Url "http://localhost:3000/fallback.html" -Name "Fallback page"
Test-HttpConnection -Url "http://localhost:8000/api/health-check" -Name "Backend health check"

# Test WebSocket connections
Write-Host "`nTesting WebSocket connections:" -ForegroundColor Yellow

function Test-WebSocketConnection {
    param(
        [string]$Url,
        [string]$Name
    )

    Write-Host "Testing WebSocket connection to $Name ($Url)..." -ForegroundColor Yellow

    try {
        # Create a simple HTML file with JavaScript to test WebSocket connection
        $tempFile = [System.IO.Path]::GetTempFileName() + ".html"
        $wsTestHtml = @"
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <script>
        let socket;
        let connectionStatus = 'unknown';

        function connectWebSocket() {
            try {
                socket = new WebSocket('$Url');

                socket.onopen = function(e) {
                    document.getElementById('status').textContent = 'Connected';
                    document.getElementById('status').style.color = 'green';
                    connectionStatus = 'connected';
                };

                socket.onclose = function(e) {
                    document.getElementById('status').textContent = 'Disconnected: ' + e.code + ' ' + e.reason;
                    document.getElementById('status').style.color = 'red';
                    connectionStatus = 'disconnected';
                };

                socket.onerror = function(e) {
                    document.getElementById('status').textContent = 'Error';
                    document.getElementById('status').style.color = 'red';
                    connectionStatus = 'error';
                };

                socket.onmessage = function(e) {
                    document.getElementById('messages').textContent += 'Received: ' + e.data + '\n';
                };
            } catch (error) {
                document.getElementById('status').textContent = 'Error: ' + error.message;
                document.getElementById('status').style.color = 'red';
                connectionStatus = 'error';
            }
        }

        function getStatus() {
            return connectionStatus;
        }

        function closeConnection() {
            if (socket) {
                socket.close();
            }
        }

        window.onload = connectWebSocket;
    </script>
</head>
<body>
    <h1>WebSocket Test</h1>
    <p>Status: <span id="status">Connecting...</span></p>
    <pre id="messages"></pre>
</body>
</html>
"@
        Set-Content -Path $tempFile -Value $wsTestHtml

        # Open the HTML file in the default browser
        Write-Host "Opening WebSocket test page in browser..." -ForegroundColor Yellow
        Start-Process $tempFile

        # Wait for user to check the connection
        $response = Read-Host "Check the browser window. Is the WebSocket connection successful? (y/n)"

        if ($response -eq "y") {
            Write-Host "✅ WebSocket connection to $Name successful" -ForegroundColor Green
        }
        else {
            Write-Host "❌ WebSocket connection to $Name failed" -ForegroundColor Red
            Write-Host "Please check the browser console for more details" -ForegroundColor Yellow
        }

        # Clean up the temporary file
        Remove-Item -Path $tempFile -Force
    }
    catch {
        Write-Host "❌ Error testing WebSocket connection to ${Name}: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test WebSocket connections
Test-WebSocketConnection -Url "ws://localhost:8000/ws/app_builder/" -Name "App Builder WebSocket"
Test-WebSocketConnection -Url "ws://localhost:8000/ws/test/" -Name "Test WebSocket"

# Check Docker volume mounts
Write-Host "`nChecking Docker volume mounts:" -ForegroundColor Yellow

# Define project name for volume prefixes
$projectName = "app-builder-201"

# Check if Docker is running
try {
    docker info > $null 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker is running" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Docker is not running" -ForegroundColor Red
        Write-Host "Please start Docker and try again." -ForegroundColor Yellow
        exit
    }
}
catch {
    Write-Host "❌ Error checking Docker status: $_" -ForegroundColor Red
    exit
}

# List project volumes
Write-Host "`nListing Docker volumes for ${projectName}:" -ForegroundColor Yellow
$volumes = docker volume ls --format "{{.Name}}" | Where-Object { $_ -like "*$projectName*" }

if ($volumes) {
    Write-Host "Found the following project volumes:" -ForegroundColor Green
    foreach ($volume in $volumes) {
        Write-Host "  - $volume" -ForegroundColor Green
    }
}
else {
    Write-Host "❌ No volumes found for $projectName" -ForegroundColor Red
    Write-Host "You may need to create the volumes by running 'docker-compose up -d'" -ForegroundColor Yellow
}

# Check if containers are running
$runningContainers = docker-compose ps --services --filter "status=running" 2>&1
if ($LASTEXITCODE -eq 0 -and $runningContainers) {
    Write-Host "`nRunning containers:" -ForegroundColor Yellow
    foreach ($container in $runningContainers) {
        if ($container -and $container.ToString().Trim()) {
            Write-Host "  - $container" -ForegroundColor Green
        }
    }

    # Check volume mounts in containers
    Write-Host "`nChecking volume mounts in containers:" -ForegroundColor Yellow

    # Check frontend volume mounts
    if ($runningContainers -contains "frontend") {
        Write-Host "`nFrontend volume mounts:" -ForegroundColor Yellow
        $frontendMounts = docker inspect --format='{{range .Mounts}}{{.Source}} -> {{.Destination}}{{println}}{{end}}' app-builder-201-frontend-1 2>&1
        if ($LASTEXITCODE -eq 0) {
            foreach ($mount in $frontendMounts) {
                Write-Host "  - $mount" -ForegroundColor Green
            }
        }
        else {
            Write-Host "❌ Could not inspect frontend container mounts" -ForegroundColor Red
        }
    }

    # Check backend volume mounts
    if ($runningContainers -contains "backend") {
        Write-Host "`nBackend volume mounts:" -ForegroundColor Yellow
        $backendMounts = docker inspect --format='{{range .Mounts}}{{.Source}} -> {{.Destination}}{{println}}{{end}}' app-builder-201-backend-1 2>&1
        if ($LASTEXITCODE -eq 0) {
            foreach ($mount in $backendMounts) {
                Write-Host "  - $mount" -ForegroundColor Green
            }
        }
        else {
            Write-Host "❌ Could not inspect backend container mounts" -ForegroundColor Red
        }
    }

    # Check database volume mounts
    if ($runningContainers -contains "db") {
        Write-Host "`nDatabase volume mounts:" -ForegroundColor Yellow
        $dbMounts = docker inspect --format='{{range .Mounts}}{{.Source}} -> {{.Destination}}{{println}}{{end}}' app-builder-201-db-1 2>&1
        if ($LASTEXITCODE -eq 0) {
            foreach ($mount in $dbMounts) {
                Write-Host "  - $mount" -ForegroundColor Green
            }
        }
        else {
            Write-Host "❌ Could not inspect database container mounts" -ForegroundColor Red
        }
    }
}
else {
    Write-Host "❌ No containers are running" -ForegroundColor Red
    Write-Host "You need to start the containers with 'docker-compose up -d' to check volume mounts" -ForegroundColor Yellow
}

Write-Host "`nDiagnostics complete!" -ForegroundColor Cyan
Write-Host "For more detailed volume management, use the scripts\manage-volumes.ps1 script" -ForegroundColor Yellow