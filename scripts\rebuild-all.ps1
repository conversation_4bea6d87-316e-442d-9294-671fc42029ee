# PowerShell script to rebuild all containers with the latest changes

Write-Host "Stopping all containers..." -ForegroundColor Yellow
docker-compose down

# Clean up any dangling images and volumes
Write-Host "Cleaning up dangling images and volumes..." -ForegroundColor Yellow
docker system prune -f

# Rebuild all containers with no cache
Write-Host "Rebuilding all containers (no cache)..." -ForegroundColor Yellow
docker-compose build --no-cache

# Start the containers
Write-Host "Starting containers..." -ForegroundColor Yellow
docker-compose up -d

Write-Host "Rebuild complete!" -ForegroundColor Green
Write-Host "Logs will follow. Press Ctrl+C to exit logs (containers will continue running)." -ForegroundColor Yellow

# Follow the logs
docker-compose logs -f