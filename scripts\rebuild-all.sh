#!/bin/bash
# Script to rebuild all containers with the latest changes

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Stopping all containers...${NC}"
docker-compose down

# Clean up any dangling images and volumes
echo -e "${YELLOW}Cleaning up dangling images and volumes...${NC}"
docker system prune -f

# Rebuild all containers with no cache
echo -e "${YELLOW}Rebuilding all containers (no cache)...${NC}"
docker-compose build --no-cache

# Start the containers
echo -e "${YELLOW}Starting containers...${NC}"
docker-compose up -d

echo -e "${GREEN}Rebuild complete!${NC}"
echo -e "${YELLOW}Logs will follow. Press Ctrl+C to exit logs (containers will continue running).${NC}"

# Follow the logs
docker-compose logs -f