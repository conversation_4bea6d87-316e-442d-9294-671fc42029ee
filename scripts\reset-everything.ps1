# PowerShell script to completely reset and rebuild the development environment

Write-Host "Stopping all containers..." -ForegroundColor Yellow
docker-compose down

Write-Host "Removing all containers, networks, and volumes..." -ForegroundColor Yellow
docker-compose down -v

Write-Host "Cleaning up Docker system..." -ForegroundColor Yellow
docker system prune -f

# Check if ports 3000 and 8000 are in use
Write-Host "Checking if ports are in use..." -ForegroundColor Yellow
$port3000 = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
$port8000 = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue

if ($port3000) {
    $processId = $port3000.OwningProcess
    $processName = (Get-Process -Id $processId).ProcessName
    Write-Host "Port 3000 is in use by process $processName (PID: $processId). Please close it manually." -ForegroundColor Red
}

if ($port8000) {
    $processId = $port8000.OwningProcess
    $processName = (Get-Process -Id $processId).ProcessName
    Write-Host "Port 8000 is in use by process $processName (PID: $processId). Please close it manually." -ForegroundColor Red
}

# Rebuild all containers with no cache
Write-Host "Rebuilding all containers (no cache)..." -ForegroundColor Yellow
docker-compose build --no-cache

# Start the containers
Write-Host "Starting containers..." -ForegroundColor Yellow
docker-compose up -d

Write-Host "Rebuild complete!" -ForegroundColor Green
Write-Host "Waiting for services to start..." -ForegroundColor Yellow

# Wait for services to be ready
Start-Sleep -Seconds 10

# Check if services are running
Write-Host "Checking service status..." -ForegroundColor Yellow
$frontendStatus = docker-compose ps frontend | Select-String "Up"
$backendStatus = docker-compose ps backend | Select-String "Up"

if ($frontendStatus -and $backendStatus) {
    Write-Host "All services are running!" -ForegroundColor Green
    Write-Host "Frontend: http://localhost:3000" -ForegroundColor Green
    Write-Host "Backend: http://localhost:8000" -ForegroundColor Green
}
else {
    Write-Host "Some services failed to start. Check logs:" -ForegroundColor Red
    docker-compose logs
}

Write-Host "Following logs. Press Ctrl+C to exit logs (containers will continue running)." -ForegroundColor Yellow
docker-compose logs -f
