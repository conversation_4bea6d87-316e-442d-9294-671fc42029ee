#!/bin/bash
# Script to completely reset and rebuild the development environment

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Stopping all containers...${NC}"
docker-compose down

echo -e "${YELLOW}Removing all containers, networks, and volumes...${NC}"
docker-compose down -v

echo -e "${YELLOW}Cleaning up Docker system...${NC}"
docker system prune -f

# Check if ports 3000 and 8000 are in use
echo -e "${YELLOW}Checking if ports are in use...${NC}"
PORT_3000=$(lsof -i:3000 -t || echo "")
PORT_8000=$(lsof -i:8000 -t || echo "")

if [ -n "$PORT_3000" ]; then
    echo -e "${RED}Port 3000 is in use by process $PORT_3000. Killing...${NC}"
    kill -9 $PORT_3000 || echo "Failed to kill process"
fi

if [ -n "$PORT_8000" ]; then
    echo -e "${RED}Port 8000 is in use by process $PORT_8000. Killing...${NC}"
    kill -9 $PORT_8000 || echo "Failed to kill process"
fi

# Rebuild all containers with no cache
echo -e "${YELLOW}Rebuilding all containers (no cache)...${NC}"
docker-compose build --no-cache

# Start the containers
echo -e "${YELLOW}Starting containers...${NC}"
docker-compose up -d

echo -e "${GREEN}Rebuild complete!${NC}"
echo -e "${YELLOW}Waiting for services to start...${NC}"

# Wait for services to be ready
sleep 10

# Check if services are running
echo -e "${YELLOW}Checking service status...${NC}"
FRONTEND_STATUS=$(docker-compose ps frontend | grep "Up" || echo "")
BACKEND_STATUS=$(docker-compose ps backend | grep "Up" || echo "")

if [ -n "$FRONTEND_STATUS" ] && [ -n "$BACKEND_STATUS" ]; then
    echo -e "${GREEN}All services are running!${NC}"
    echo -e "${GREEN}Frontend: http://localhost:3000${NC}"
    echo -e "${GREEN}Backend: http://localhost:8000${NC}"
else
    echo -e "${RED}Some services failed to start. Check logs:${NC}"
    docker-compose logs
fi

echo -e "${YELLOW}Following logs. Press Ctrl+C to exit logs (containers will continue running).${NC}"
docker-compose logs -f